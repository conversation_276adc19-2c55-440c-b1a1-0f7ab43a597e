import React, { useState } from 'react';
import axios from 'axios';

const PredictionForm = () => {
  const [formData, setFormData] = useState({
    pregnancies: '',
    glucose: '',
    bloodPressure: '',
    skinThickness: '',
    insulin: '',
    bmi: '',
    diabetesPedigree: '',
    age: ''
  });

  const [prediction, setPrediction] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setPrediction('');

    try {
      // For now, we'll use a mock prediction since we don't have a backend yet
      // In a real application, you would make an API call to your backend
      // const response = await axios.post('/api/predictions', formData);
      
      // Mock prediction logic (this is just for demonstration)
      // In a real app, this would come from your trained model on the backend
      const mockPrediction = Math.random() > 0.5 ? "Diabetes Milletus" : "Not Milletus";
      
      setTimeout(() => {
        setPrediction(`It is ${mockPrediction}`);
        setLoading(false);
      }, 1000);
      
    } catch (err) {
      setError('Error making prediction. Please try again.');
      setLoading(false);
      console.error('Prediction error:', err);
    }
  };

  return (
    <div>
      <form onSubmit={handleSubmit}>
        <input
          type="text"
          name="pregnancies"
          placeholder="No of Pregnancies"
          required
          value={formData.pregnancies}
          onChange={handleChange}
        />
        <input
          type="text"
          name="glucose"
          placeholder="Plasma Glucose"
          required
          value={formData.glucose}
          onChange={handleChange}
        />
        <input
          type="text"
          name="bloodPressure"
          placeholder="Blood Pressure"
          required
          value={formData.bloodPressure}
          onChange={handleChange}
        />
        <input
          type="text"
          name="skinThickness"
          placeholder="Skin Thickness"
          required
          value={formData.skinThickness}
          onChange={handleChange}
        />
        <input
          type="text"
          name="insulin"
          placeholder="Insulin"
          required
          value={formData.insulin}
          onChange={handleChange}
        />
        <input
          type="text"
          name="bmi"
          placeholder="Body Mass Index"
          required
          value={formData.bmi}
          onChange={handleChange}
        />
        <input
          type="text"
          name="diabetesPedigree"
          placeholder="Diabetes Pedigree Function"
          required
          value={formData.diabetesPedigree}
          onChange={handleChange}
        />
        <input
          type="text"
          name="age"
          placeholder="Age"
          required
          value={formData.age}
          onChange={handleChange}
        />
        <button type="submit" className="btn btn-primary btn-block btn-large" disabled={loading}>
          {loading ? 'Predicting...' : 'Predict'}
        </button>
      </form>

      {error && <div className="error-message">{error}</div>}
      {prediction && <div className="prediction-result">{prediction}</div>}
    </div>
  );
};

export default PredictionForm;
