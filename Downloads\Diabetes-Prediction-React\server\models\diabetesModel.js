/**
 * A simple JavaScript implementation of a logistic regression model for diabetes prediction
 * This is a simplified version for demonstration purposes
 * In a real application, you would use a more sophisticated model trained on the data
 */

// These are mock coefficients for demonstration
// In a real application, these would be derived from training a model on the dataset
const coefficients = [
  0.1, // Pregnancies
  0.01, // Glucose
  -0.005, // Blood Pressure
  0.002, // Skin Thickness
  0.0003, // Insulin
  0.08, // BMI
  0.3, // Diabetes Pedigree
  0.02 // Age
];

const intercept = -2.5;

// Sigmoid function for logistic regression
const sigmoid = (z) => {
  return 1 / (1 + Math.exp(-z));
};

/**
 * Predicts the probability of diabetes based on input features
 * @param {Array} features - Array of input features [pregnancies, glucose, bloodPressure, skinThickness, insulin, bmi, diabetesPedigree, age]
 * @returns {Number} - Probability of diabetes (0-1)
 */
const predict = (features) => {
  if (!Array.isArray(features) || features.length !== 8) {
    throw new Error('Input must be an array of 8 features');
  }

  // Calculate the linear combination of features and coefficients
  let z = intercept;
  for (let i = 0; i < features.length; i++) {
    z += features[i] * coefficients[i];
  }

  // Apply sigmoid function to get probability
  const probability = sigmoid(z);
  
  return probability;
};

module.exports = {
  predict
};
