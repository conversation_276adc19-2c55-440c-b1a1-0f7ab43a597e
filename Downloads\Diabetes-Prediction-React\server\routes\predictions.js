const express = require('express');
const router = express.Router();
const { predict } = require('../models/diabetesModel');

// @route   POST api/predictions
// @desc    Make a diabetes prediction
// @access  Public
router.post('/', async (req, res) => {
  try {
    const inputData = req.body;
    
    // Convert input data to array format expected by the model
    const inputArray = [
      parseFloat(inputData.pregnancies),
      parseFloat(inputData.glucose),
      parseFloat(inputData.bloodPressure),
      parseFloat(inputData.skinThickness),
      parseFloat(inputData.insulin),
      parseFloat(inputData.bmi),
      parseFloat(inputData.diabetesPedigree),
      parseFloat(inputData.age)
    ];
    
    // Make prediction
    const prediction = predict(inputArray);
    
    // Determine result
    const result = prediction > 0.5 ? "Diabetes Milletus" : "Not Milletus";
    
    res.json({ 
      success: true, 
      prediction: prediction,
      result: result
    });
  } catch (error) {
    console.error('Error making prediction:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Server error during prediction' 
    });
  }
});

module.exports = router;
